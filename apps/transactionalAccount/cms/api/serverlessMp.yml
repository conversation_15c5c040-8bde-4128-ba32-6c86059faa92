service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-cms

plugins:
  - serverless-dotenv-plugin
  - serverless-esbuild
  - serverless-pseudo-parameters
  - serverless-plugin-resource-tagging
  - serverless-step-functions
  - serverless-prune-plugin

useDotenv: true
variablesResolutionMode: ********
frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: ${opt:region}
  stackName: ${self:service}
  accountId: '#{AWS::AccountId}'
  vpc:
    securityGroupIds:
      - 'Fn::ImportValue': '${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg'
    subnetIds:
      - 'Fn::ImportValue': '${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01'
      - 'Fn::ImportValue': '${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02'
      - 'Fn::ImportValue': '${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03'
  layers:
    - arn:aws:lambda:${opt:region}:${self:custom.lambdaParameterExtensionAccountId}:layer:AWS-Parameters-and-Secrets-Lambda-Extension:11
  bucketStackName: ${env:COMPONENT_NAME}-${env:PART_NAME}-iac-s3
  timeout: ${env:LAMBDA_TIMEOUT_IN_SECONDS}
  deploymentBucket:
    name: ${cf:${self:provider.bucketStackName}.deploymentBucket}
    maxPreviousDeploymentArtifacts: 100
    blockPublicAccess: true
  appsyncStackName: ${self:custom.service}-appsync
  iamUserKey: ${ssm:/aws/reference/secretsmanager/${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}/IAM_USER_KEY}
  iamUserSecret: ${ssm:/aws/reference/secretsmanager/${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}/IAM_USER_SECRET}
  appSyncEndpoint:
    Fn::ImportValue: !Sub '${self:provider.appsyncStackName}-graphQlApiUrl'
  appSyncApiId:
    Fn::ImportValue: !Sub '${self:provider.appsyncStackName}-graphQlApiId'
  dynamodbStackName: ${self:custom.serviceName}-dynamodb
  sessionCacheTableName: ${self:provider.dynamodbStackName}-${env:SESSION_CACHE_TABLE}
  entityTableName: ${self:provider.dynamodbStackName}-${env:COMPONENT_TABLE}
  merchantTableName: ${self:provider.dynamodbStackName}-${env:MERCHANT_TABLE}
  bankingMigrationTableName: ${self:provider.stackName}-bankingMigration
  auth0ClientId: ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/AUTH0_CLIENT_ID}
  auth0ClientSecret: ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/AUTH0_CLIENT_SECRET}
  siteGsi: ${env:SITE_GSI}
  entityCacheGsi: ${env:ENTITY_CACHE_GSI}
  entityGsi: ${env:ENTITY_GSI}
  typeGsi: ${env:TYPE_GSI}
  debitCardIdGsi: ${env:DEBIT_CARD_ID_GSI}
  cmsStackName: ${env:STATIC_ENV_NAME}-cms-cqrs-apiHandlers
  amsApiEndpoint: ${ssm:${opt:stage}-ams-engine-api-endpoint, ''}
  amsApiEndpointVersion: ${env:AMS_API_ENDPOINT_VERSION}
  serviceName: ${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}
  service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}
  environment:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    LOG_LEVEL: ${env:LOG_LEVEL}
    SESSION_CACHE_TABLE: ${self:provider.sessionCacheTableName}
    COMPONENT_TABLE: ${self:provider.entityTableName}
    MERCHANT_TABLE: ${self:provider.merchantTableName}
    AUTH0_CLIENT_ID: ${self:provider.auth0ClientId}
    AUTH0_CLIENT_SECRET: ${self:provider.auth0ClientSecret}

    CQRS_COMMAND_HANDLER: ${self:custom.mpCqrsCommandHandler}
    IS_ZELLER_SESSION_ID_ENABLED: ${env:IS_ZELLER_SESSION_ID_ENABLED}
    ZELLER_APP_FIREBASE_ADMIN_PRIVATE_KEY_SSM_NAME: ${self:custom.firebaseAdminPrivateKeySsmName}
    ZELLER_APP_FIREBASE_ADMIN_EMAIL_SSM_NAME: ${self:custom.firebaseAdminEmailSsmName}

    IS_RBAC_ENFORCED: ${env:IS_RBAC_ENFORCED}
    MULTI_ENTITY_ENABLED: ${env:MULTI_ENTITY_ENABLED}
    IS_RBAC_ENFORCE_ROLE: ${env:IS_RBAC_ENFORCE_ROLE}
  tags:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    service: ${env:COMPONENT_NAME}-${env:PART_NAME}
    env: ${opt:stage}

  stackTags: ${self:provider.tags}

package:
  individually: true
  patterns:
    - '!node_modules/**'
    - 'src/fonts/*'

custom:
  esbuild:
    bundle: true
    keepNames: true
    zipConcurrency: 6 #heap failures
    exclude:
      - 'cache-manager'
      - 'class-transformer'
      - 'class-validator'
      - '@nestjs/microservices'
      - '@nestjs/websockets/socket-module'
      - '@nestjs/platform-express'
    external:
      - pdfkit
      - 'sharp'
      - 'heic-convert'
      - 'pngjs'
    plugins: esbuild_plugin.js
    packagerOptions:
      scripts:
        - rm -rf node_modules/sharp
        - SHARP_IGNORE_GLOBAL_LIBVIPS=1 npm_config_arch=x64 npm_config_platform=linux npm install --cpu=x64 --os=linux sharp@0.33.4

  prune:
    automatic: true
    includeLayers: true
    number: 5
  firebaseAdminPrivateKeySsmName: /${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/ZELLER_APP_FIREBASE_ADMIN_PRIVATE_KEY
  firebaseAdminEmailSsmName: /${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/ZELLER_APP_FIREBASE_ADMIN_EMAIL
  zellerAppAuth0ClientId: ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/ZELLER_APP_AUTH0_CLIENT_ID, 'none'}
  service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}
  serviceName: ${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}
  cmsEndpoint: '${ssm:${env:STATIC_ENV_NAME}-bankingwrapper-engine-api-endpoint}/'
  richDataStackName: ${self:provider.serviceName}-richdata
  auth0Audience: ${env:IDENTITY_AUTH0_AUDIENCE}
  auth0Tenant: ${env:IDENTITY_AUTH0_TENANT}
  appsyncDataSourceRoleArn: ${cf:${self:provider.appsyncStackName}.DataSourceLambdaRole}
  debitCardTransactionSummaryBucket: ${ssm:${self:custom.serviceName}-assets-txn-de-bucket}
  getMerchantDetailsLambda: ${self:custom.richDataStackName}-getMerchantDetails
  mpCqrsStackName: ${opt:stage}-mp-cqrs
  mpCqrsCommandHandler: ${self:custom.mpCqrsStackName}-commandHandlers-handler
  cqrsStackName:
    dev: '${opt:stage}-mp-cqrs'
    staging: '${opt:stage}-mp-cqrs'
    prod: '${opt:stage}-mp-cqrs'
    st: '${opt:stage}-mp-cms-cqrs'
  mpCqrsEventBus: '${cf:${self:custom.cqrsStackName.${opt:stage}, "${self:custom.cqrsStackName.st}"}-iac-eventBridge.EventBusProjectionArn}'
  billingApiEndpoint: ${ssm:${env:STATIC_ENV_NAME}-ss-cqrs-api-endpoint}/${env:BILLING_API_ENDPOINT_VERSION}
  exportTransactionsBucket: ${ssm:${self:custom.serviceName}-assets-txn-de-bucket}
  iamUserKey: ${ssm:/aws/reference/secretsmanager/${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}/IAM_USER_KEY}
  iamUserSecret: ${ssm:/aws/reference/secretsmanager/${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}/IAM_USER_SECRET}
  appSyncEndpoint:
    Fn::ImportValue: !Sub '${self:provider.appsyncStackName}-graphQlApiUrl'
  tableDeletionProtection:
    dev: true
    staging: true
    prod: true
    st: false

  domicileLookupTableReadRolePolicyArn:
    Fn::ImportValue: '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
  lambdaParameterExtensionAccountId: '${ssm:Parameters-and-Secrets-Lambda-Extension}'
stepFunctions:
  stateMachines:
functions:
  - ${file(resources/mp/lambda.yml)}

resources:
  - ${file(resources/mp/resolvers.yml)}
  - ${file(resources/mp/iam.yml)}
  - ${file(resources/mp/resources.yml)}
