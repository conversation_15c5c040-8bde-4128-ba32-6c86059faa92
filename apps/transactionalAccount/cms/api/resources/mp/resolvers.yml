Resources:
  updateDbtCardTxnAnnotnsDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: updateDbtCardTxnAnnotnsDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: AWS_LAMBDA
      ServiceRoleArn: ${self:custom.appsyncDataSourceRoleArn}
      LambdaConfig:
        LambdaFunctionArn: !GetAtt UpdateDbtCardTxnAnnotnsHandlerLambdaFunction.Arn

  updateDbtCardTxnAnnotnsResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: updateDebitCardTransactionAnnotations
      TypeName: Mutation
      RequestMappingTemplate: |
        {
          "version" : "2018-05-29",
          "operation": "Invoke",
          "payload": { "args": $util.toJson($context.args), "identity": $util.toJson($context.identity), "request": $util.toJson($context.request), "authType": $util.toJson($util.authType()), "source": $util.toJson($context.source), "info": $util.toJson($context.info)}
        }
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt updateDbtCardTxnAnnotnsDataSource.Name

  getDebitCardTxnSummaryDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: getDebitCardTransactionSummaryDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: AWS_LAMBDA
      ServiceRoleArn: ${self:custom.appsyncDataSourceRoleArn}
      LambdaConfig:
        LambdaFunctionArn: !GetAtt GetDebitCardTxnSummaryHandlerLambdaFunction.Arn

  getDebitCardTxnSummaryResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: getDebitCardTransactionSummary
      TypeName: Query
      RequestMappingTemplate: |
        {
          "version" : "2018-05-29",
          "operation": "Invoke",
          "payload": { "args": $util.toJson($context.args), "identity": $util.toJson($context.identity), "request": $util.toJson($context.request), "authType": $util.toJson($util.authType()), "source": $util.toJson($context.source), "info": $util.toJson($context.info)}
        }
      ResponseMappingTemplate: ${file(resources/mp/template/response.vtl)}
      DataSourceName: !GetAtt getDebitCardTxnSummaryDataSource.Name

  exportDebitCardTransactionsDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: exportDebitCardTransactionsDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: AWS_LAMBDA
      ServiceRoleArn: ${self:custom.appsyncDataSourceRoleArn}
      LambdaConfig:
        LambdaFunctionArn: !GetAtt ExportDebitCardTransactionsHandlerLambdaFunction.Arn

  exportDebitCardTransactionsResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: exportDebitCardTransactions
      TypeName: Subscription
      RequestMappingTemplate: ${file(resources/mp/template/getDebitCardTxnsRequest.vtl)}
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt exportDebitCardTransactionsDataSource.Name

  publishDebitCardTransactionsDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: publishDebitCardTransactionsDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: NONE

  publishDebitCardTransactionsResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: publishDebitCardTransactionsExport
      TypeName: Mutation
      RequestMappingTemplate: |
        {
          "version": "2017-02-28",
          "payload": $utils.toJson($context.arguments.input)
        }
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt publishDebitCardTransactionsDataSource.Name
