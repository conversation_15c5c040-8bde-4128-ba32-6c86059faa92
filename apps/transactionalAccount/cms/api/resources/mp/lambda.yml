updateDbtCardTxnAnnotnsHandler:
  handler: src/lambda/mp/cmsLambda.updateDbtCardTxnAnnotnsHandler
  name: ${self:provider.stackName}-updateDbtCardTxnAnnotnsHandler
  tracing: true

  role: updateDbtCardTxnAnnotnsRole
  environment:
    CMS_API_ENDPOINT: ${self:custom.cmsEndpoint}
    COMPONENT_TABLE: ${self:provider.entityTableName}
    SESSION_CACHE_TABLE: ${self:provider.sessionCacheTableName}
    AUTH0_TENANT: ${self:custom.auth0Tenant}
    AUTH0_CLIENT_ID: ${self:provider.auth0ClientId}
    AUTH0_CLIENT_SECRET: ${self:provider.auth0ClientSecret}

getDebitCardTxnSummaryHandler:
  handler: src/lambda/mp/debitCardExportLambda.getDebitCardTxnSummaryHandler
  name: ${self:provider.stackName}-getDebitCardTxnSummaryHandler
  role: getDebitCardTxnSummaryRole
  tracing: true

  environment:
    COMPONENT_TABLE: ${self:provider.entityTableName}
    SESSION_CACHE_TABLE: ${self:provider.sessionCacheTableName}
    MERCHANT_TABLE: ${self:provider.merchantTableName}
    AUTH0_TENANT: ${self:custom.auth0Tenant}
    AUTH0_CLIENT_ID: ${self:provider.auth0ClientId}
    AUTH0_CLIENT_SECRET: ${self:provider.auth0ClientSecret}
    DEBIT_CARD_TRANSACTION_SUMMARY_BUCKET: ${self:custom.debitCardTransactionSummaryBucket}

exportDebitCardTransactionsHandler:
  handler: src/lambda/mp/debitCardTransactionsExportLambda.exportDebitCardTransactionsHandler
  name: ${self:provider.stackName}-exportDcaTxns
  role: exportDebitCardTransactionsRole
  tracing: true
  environment:
    COMPONENT_TABLE: ${self:provider.entityTableName}
    SESSION_CACHE_TABLE: ${self:provider.sessionCacheTableName}
    PUBLISH_HANDLER_NAME: ${self:provider.stackName}-publishDcaTxnsExport

publishDebitCardTransactionsExportHandler:
  handler: src/lambda/mp/debitCardTransactionsExportLambda.publishDebitCardTransactionsExportHandler
  name: ${self:provider.stackName}-publishDcaTxnsExport
  role: publishDebitCardTransactionsExportRole
  tracing: true
  memorySize: 2048
  timeout: 600

  environment:
    COMPONENT_TABLE: ${self:provider.entityTableName}
    SESSION_CACHE_TABLE: ${self:provider.sessionCacheTableName}
    MERCHANT_TABLE: ${self:provider.merchantTableName}
    TRANSACTION_EXPORT_BUCKET: ${self:custom.exportTransactionsBucket}
    IAM_USER_KEY: ${self:custom.iamUserKey}
    IAM_USER_SECRET: ${self:custom.iamUserSecret}
    APP_SYNC_ENDPOINT: '${self:custom.appSyncEndpoint}'
